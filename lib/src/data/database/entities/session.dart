import 'package:objectbox/objectbox.dart';

/// Session entity for storing user session information
/// UID range: 1001-1009
@Entity()
class Session {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Unique session key for identification
  @Unique()
  @Property(uid: 1001)
  String sessionKey = '';

  /// Session ID from server
  @Property(uid: 1002)
  String sessionId = '';

  /// When the user logged out (null if still active)
  @Property(uid: 1003)
  DateTime? logoutTime;

  /// When the user logged in
  @Property(uid: 1004)
  DateTime loginTime = DateTime.now();

  /// Session token for authentication
  @Property(uid: 1005)
  String sessionToken = '';

  /// Whether this session is currently active
  @Property(uid: 1006)
  bool active = true;

  /// Whether user is logged in
  @Property(uid: 1007)
  bool isLogin = false;

  /// Whether user logged in via QR code
  @Property(uid: 1008)
  bool isLoginQR = false;

  /// Whether passkey has been migrated
  @Property(uid: 1009)
  bool passkeyMigrated = false;

  // RELATIONSHIPS - Reverse relationships for all entities that reference this session

  /// Session local metadata records
  @Backlink('session')
  final localMetadata = ToMany<SessionLocalMetadata>();

  // TODO: Add these relationships when entities are updated with relationships
  // /// History records for this session
  // @Backlink('session')
  // final histories = ToMany<History>();

  // /// Manager records for this session
  // @Backlink('session')
  // final managers = ToMany<Manager>();

  /// Users in this session
  @Backlink('session')
  final users = ToMany<User>();

  // /// Channels in this session
  // @Backlink('session')
  // final channels = ToMany<Channel>();

  // /// Messages in this session
  // @Backlink('session')
  // final messages = ToMany<Message>();

  // /// Members in this session
  // @Backlink('session')
  // final members = ToMany<Member>();

  // /// Friend relationships in this session
  // @Backlink('session')
  // final friends = ToMany<Friend>();

  // /// Profiles in this session
  // @Backlink('session')
  // final profiles = ToMany<Profile>();

  // /// Visited profiles in this session
  // @Backlink('session')
  // final visitedProfiles = ToMany<VisitedProfile>();

  // /// Private data records in this session
  // @Backlink('session')
  // final privateData = ToMany<PrivateData>();

  // /// User private data records in this session
  // @Backlink('session')
  // final userPrivateData = ToMany<UserPrivateData>();

  // /// Channel private data records in this session
  // @Backlink('session')
  // final channelPrivateData = ToMany<ChannelPrivateData>();

  // /// Call logs in this session
  // @Backlink('session')
  // final callLogs = ToMany<CallLog>();

  // /// Call log private data in this session
  // @Backlink('session')
  // final callLogPrivateData = ToMany<CallLogPrivateData>();

  // /// Stickers in this session
  // @Backlink('session')
  // final stickers = ToMany<Sticker>();

  // /// Collections in this session
  // @Backlink('session')
  // final collections = ToMany<Collection>();

  // /// Sticker frame counts in this session
  // @Backlink('session')
  // final stickerFrameCounts = ToMany<StickerFrameCount>();

  // /// Translated results in this session
  // @Backlink('session')
  // final translatedResults = ToMany<TranslatedResult>();

  /// User presence records in this session
  @Backlink('session')
  final userPresences = ToMany<UserPresence>();

  // /// User status records in this session
  // @Backlink('session')
  // final userStatuses = ToMany<UserStatus>();

  /// Default constructor
  Session();

  /// Constructor with required fields
  Session.create({
    required this.sessionKey,
    required this.sessionId,
    required this.sessionToken,
    DateTime? loginTime,
    DateTime? logoutTime,
  }) {
    this.loginTime = loginTime ?? DateTime.now();
    this.logoutTime = logoutTime;
  }

  /// Copy constructor for updates
  Session copyWith({
    int? id,
    String? sessionKey,
    String? sessionId,
    DateTime? logoutTime,
    DateTime? loginTime,
    String? sessionToken,
    bool? active,
    bool? isLogin,
    bool? isLoginQR,
    bool? passkeyMigrated,
  }) {
    return Session()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..sessionId = sessionId ?? this.sessionId
      ..logoutTime = logoutTime ?? this.logoutTime
      ..loginTime = loginTime ?? this.loginTime
      ..sessionToken = sessionToken ?? this.sessionToken
      ..active = active ?? this.active
      ..isLogin = isLogin ?? this.isLogin
      ..isLoginQR = isLoginQR ?? this.isLoginQR
      ..passkeyMigrated = passkeyMigrated ?? this.passkeyMigrated;
  }

  /// Mark session as logged out
  void logout() {
    active = false;
    isLogin = false;
    logoutTime = DateTime.now();
  }

  /// Mark session as logged in
  void login({bool isQR = false}) {
    active = true;
    isLogin = true;
    isLoginQR = isQR;
    loginTime = DateTime.now();
    logoutTime = null;
  }

  @override
  String toString() {
    return 'Session{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'sessionId: $sessionId, '
        'logoutTime: $logoutTime, '
        'loginTime: $loginTime, '
        'sessionToken: $sessionToken, '
        'active: $active, '
        'isLogin: $isLogin, '
        'isLoginQR: $isLoginQR, '
        'passkeyMigrated: $passkeyMigrated'
        '}';
  }
}
