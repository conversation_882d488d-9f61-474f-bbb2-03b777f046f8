import 'package:objectbox/objectbox.dart';
import '../utils/relationship_manager.dart';
import 'session.dart';

/// User entity for storing user information
/// UID range: 2001-2013
@Entity()
class User {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed, NEW FIELD)
  @Index()
  @Property(uid: 2013)
  String sessionKey = '';

  /// Unique user ID (indexed)
  @Unique()
  @Index()
  @Property(uid: 2001)
  String userId = '';

  // RELATIONSHIPS

  /// ToOne relationship to Session
  final session = ToOne<Session>();

  /// Reverse relationships - All entities that reference this user

  /// User profiles
  @Backlink('user')
  final profiles = ToMany<Profile>();

  // TODO: Add these relationships when entities are updated with relationships
  // /// Visited profiles where this user was visited
  // @Backlink('visitedUser')
  // final visitedProfiles = ToMany<VisitedProfile>();

  // /// Friend relationships where this user is the owner
  // @Backlink('ownerUser')
  // final ownedFriendships = ToMany<Friend>();

  // /// Friend relationships where this user is the friend
  // @Backlink('friendUser')
  // final friendships = ToMany<Friend>();

  // /// Private data records for this user
  // @Backlink('user')
  // final privateData = ToMany<PrivateData>();

  // /// User private data records for this user
  // @Backlink('user')
  // final userPrivateData = ToMany<UserPrivateData>();

  /// User presence records for this user
  @Backlink('user')
  final presences = ToMany<UserPresence>();

  // /// User status records for this user
  // @Backlink('user')
  // final statuses = ToMany<UserStatus>();

  // /// Messages sent by this user
  // @Backlink('user')
  // final sentMessages = ToMany<Message>();

  // /// Call logs where this user is the caller
  // @Backlink('callerUser')
  // final outgoingCalls = ToMany<CallLog>();

  // /// Call logs where this user is the callee
  // @Backlink('calleeUser')
  // final incomingCalls = ToMany<CallLog>();

  // /// Channel memberships for this user
  // @Backlink('user')
  // final channelMemberships = ToMany<Member>();

  // /// Channels owned by this user
  // @Backlink('ownerUser')
  // final ownedChannels = ToMany<Channel>();

  // /// Channels where this user is the recipient
  // @Backlink('recipientUser')
  // final recipientChannels = ToMany<Channel>();

  /// Username
  @Property(uid: 2002)
  String username = '';

  /// User creation time
  @Property(uid: 2003)
  DateTime? createTime;

  /// User last update time
  @Property(uid: 2004)
  DateTime? updateTime;

  /// User type (integer enum)
  @Property(uid: 2005)
  int userType = 0;

  /// User connect link
  @Property(uid: 2006)
  String userConnectLink = '';

  /// Media permission setting
  @Property(uid: 2007)
  int mediaPermissionSetting = 0;

  /// Global notification status
  @Property(uid: 2009)
  bool globalNotificationStatus = true;

  /// SIP credentials
  @Property(uid: 2010)
  String sipCredentials = '';

  /// SIP address
  @Property(uid: 2011)
  String sipAddress = '';

  /// Whether this is a partial user record
  @Property(uid: 2012)
  bool isPartial = false;

  /// Composite index field: sessionUserId
  String get sessionUserId => '${sessionKey}_$userId';

  /// Default constructor
  User();

  /// Constructor with required fields
  User.create({
    required this.sessionKey,
    required this.userId,
    this.username = '',
    this.createTime,
    this.updateTime,
    this.userType = 0,
    this.userConnectLink = '',
    this.mediaPermissionSetting = 0,
    this.globalNotificationStatus = true,
    this.sipCredentials = '',
    this.sipAddress = '',
    this.isPartial = false,
  }) {
    this.createTime ??= DateTime.now();
    this.updateTime ??= DateTime.now();
  }

  /// Copy constructor for updates
  User copyWith({
    int? id,
    String? sessionKey,
    String? userId,
    String? username,
    DateTime? createTime,
    DateTime? updateTime,
    int? userType,
    String? userConnectLink,
    int? mediaPermissionSetting,
    bool? globalNotificationStatus,
    String? sipCredentials,
    String? sipAddress,
    bool? isPartial,
  }) {
    return User()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..userId = userId ?? this.userId
      ..username = username ?? this.username
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime
      ..userType = userType ?? this.userType
      ..userConnectLink = userConnectLink ?? this.userConnectLink
      ..mediaPermissionSetting =
          mediaPermissionSetting ?? this.mediaPermissionSetting
      ..globalNotificationStatus =
          globalNotificationStatus ?? this.globalNotificationStatus
      ..sipCredentials = sipCredentials ?? this.sipCredentials
      ..sipAddress = sipAddress ?? this.sipAddress
      ..isPartial = isPartial ?? this.isPartial;
  }

  /// Update user information
  void updateInfo({
    String? username,
    int? userType,
    String? userConnectLink,
    int? mediaPermissionSetting,
    bool? globalNotificationStatus,
    String? sipCredentials,
    String? sipAddress,
  }) {
    if (username != null) this.username = username;
    if (userType != null) this.userType = userType;
    if (userConnectLink != null) this.userConnectLink = userConnectLink;
    if (mediaPermissionSetting != null)
      this.mediaPermissionSetting = mediaPermissionSetting;
    if (globalNotificationStatus != null)
      this.globalNotificationStatus = globalNotificationStatus;
    if (sipCredentials != null) this.sipCredentials = sipCredentials;
    if (sipAddress != null) this.sipAddress = sipAddress;
    updateTime = DateTime.now();
  }

  /// Mark as complete user record
  void markAsComplete() {
    isPartial = false;
    updateTime = DateTime.now();
  }

  // RELATIONSHIP HELPERS

  /// Validate that sessionKey matches the related session
  bool get isSessionRelationshipValid {
    final target = session.target;
    if (target == null) return sessionKey.isEmpty;
    return target.sessionKey == sessionKey;
  }

  /// Set session relationship by sessionKey
  void setSessionRelationship() {
    if (sessionKey.isNotEmpty) {
      RelationshipManager().setSessionRelationship(session, sessionKey);
    }
  }

  // TODO: Add these helper methods when relationships are implemented
  // /// Get all friend relationships (both owned and received)
  // List<Friend> get allFriendships {
  //   final allFriends = <Friend>[];
  //   allFriends.addAll(ownedFriendships);
  //   allFriends.addAll(friendships);
  //   return allFriends;
  // }

  // /// Get all call logs (both incoming and outgoing)
  // List<CallLog> get allCallLogs {
  //   final allCalls = <CallLog>[];
  //   allCalls.addAll(outgoingCalls);
  //   allCalls.addAll(incomingCalls);
  //   return allCalls;
  // }

  // /// Get all channels (both owned and recipient)
  // List<Channel> get allChannels {
  //   final allChannels = <Channel>[];
  //   allChannels.addAll(ownedChannels);
  //   allChannels.addAll(recipientChannels);
  //   return allChannels;
  // }

  @override
  String toString() {
    return 'User{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'userId: $userId, '
        'username: $username, '
        'createTime: $createTime, '
        'updateTime: $updateTime, '
        'userType: $userType, '
        'userConnectLink: $userConnectLink, '
        'mediaPermissionSetting: $mediaPermissionSetting, '
        'globalNotificationStatus: $globalNotificationStatus, '
        'sipCredentials: $sipCredentials, '
        'sipAddress: $sipAddress, '
        'isPartial: $isPartial'
        '}';
  }
}
