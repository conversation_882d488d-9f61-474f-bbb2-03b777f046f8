import 'package:objectbox/objectbox.dart';
import '../utils/relationship_manager.dart';
import 'session.dart';
import 'user.dart';

/// User presence entity for storing user presence information
/// UID range: 9200-9209
@Entity()
class UserPresence {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed)
  @Index()
  @Property(uid: 9200)
  String sessionKey = '';

  /// User ID (indexed)
  @Index()
  @Property(uid: 9201)
  String userId = '';

  // RELATIONSHIPS

  /// ToOne relationship to Session
  final session = ToOne<Session>();

  /// ToOne relationship to User
  final user = ToOne<User>();

  /// Presence status
  @Property(uid: 9202)
  int presenceStatus = 0;

  /// Last seen time
  @Property(uid: 9203)
  DateTime? lastSeenTime;

  /// Last active time
  @Property(uid: 9204)
  DateTime? lastActiveTime;

  /// Device information
  @Property(uid: 9205)
  String deviceInfo = '';

  /// Whether user is typing
  @Property(uid: 9206)
  bool isTyping = false;

  /// Channel ID where user is typing
  @Property(uid: 9207)
  String typingChannelId = '';

  /// Creation time
  @Property(uid: 9208)
  DateTime? createTime;

  /// Last update time
  @Property(uid: 9209)
  DateTime? updateTime;

  /// Composite index field: userIdStatus
  String get userIdStatus => '${userId}_$presenceStatus';

  /// Composite index field: sessionUserStatus
  String get sessionUserStatus => '${sessionKey}_${userId}_$presenceStatus';

  /// Composite index field: userIdTyping
  String get userIdTyping => '${userId}_$isTyping';

  /// Default constructor
  UserPresence();

  /// Constructor with required fields
  UserPresence.create({
    required this.sessionKey,
    required this.userId,
    this.presenceStatus = 0,
    this.lastSeenTime,
    this.lastActiveTime,
    this.deviceInfo = '',
    this.isTyping = false,
    this.typingChannelId = '',
    this.createTime,
    this.updateTime,
  }) {
    this.createTime ??= DateTime.now();
    this.updateTime ??= DateTime.now();
  }

  /// Copy constructor for updates
  UserPresence copyWith({
    int? id,
    String? sessionKey,
    String? userId,
    int? presenceStatus,
    DateTime? lastSeenTime,
    DateTime? lastActiveTime,
    String? deviceInfo,
    bool? isTyping,
    String? typingChannelId,
    DateTime? createTime,
    DateTime? updateTime,
  }) {
    return UserPresence()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..userId = userId ?? this.userId
      ..presenceStatus = presenceStatus ?? this.presenceStatus
      ..lastSeenTime = lastSeenTime ?? this.lastSeenTime
      ..lastActiveTime = lastActiveTime ?? this.lastActiveTime
      ..deviceInfo = deviceInfo ?? this.deviceInfo
      ..isTyping = isTyping ?? this.isTyping
      ..typingChannelId = typingChannelId ?? this.typingChannelId
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime;
  }

  /// Update presence status
  void updatePresenceStatus(int newStatus) {
    presenceStatus = newStatus;
    lastActiveTime = DateTime.now();
    updateTime = DateTime.now();
  }

  /// Set user as online
  void setOnline() {
    presenceStatus = 1; // Assuming 1 means online
    lastActiveTime = DateTime.now();
    updateTime = DateTime.now();
  }

  /// Set user as offline
  void setOffline() {
    presenceStatus = 0; // Assuming 0 means offline
    lastSeenTime = DateTime.now();
    updateTime = DateTime.now();
  }

  /// Start typing in a channel
  void startTyping(String channelId) {
    isTyping = true;
    typingChannelId = channelId;
    updateTime = DateTime.now();
  }

  /// Stop typing
  void stopTyping() {
    isTyping = false;
    typingChannelId = '';
    updateTime = DateTime.now();
  }

  /// Update device info
  void updateDeviceInfo(String newDeviceInfo) {
    deviceInfo = newDeviceInfo;
    updateTime = DateTime.now();
  }

  /// Check if user is online
  bool get isOnline => presenceStatus == 1;

  /// Check if user is offline
  bool get isOffline => presenceStatus == 0;

  /// Check if user is away
  bool get isAway => presenceStatus == 2;

  /// Check if user is busy
  bool get isBusy => presenceStatus == 3;

  // RELATIONSHIP HELPERS

  /// Helper getter for sessionKey from relationship
  String get sessionKeyFromRelation => session.target?.sessionKey ?? sessionKey;

  /// Helper setter for sessionKey - sets both field and relationship
  set sessionKeyFromRelation(String key) {
    sessionKey = key;
    if (key.isNotEmpty) {
      RelationshipManager().setSessionRelationship(session, key);
    }
  }

  /// Helper getter for userId from relationship
  String get userIdFromRelation => user.target?.userId ?? userId;

  /// Helper setter for userId - sets both field and relationship
  set userIdFromRelation(String id) {
    userId = id;
    if (id.isNotEmpty) {
      RelationshipManager().setUserRelationship(user, id);
    }
  }

  /// Validate that relationships are consistent
  bool get areRelationshipsValid {
    final sessionTarget = session.target;
    final userTarget = user.target;

    if (sessionTarget == null || userTarget == null) return false;

    return sessionTarget.sessionKey == sessionKey &&
           userTarget.userId == userId;
  }

  @override
  String toString() {
    return 'UserPresence{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'userId: $userId, '
        'presenceStatus: $presenceStatus, '
        'lastSeenTime: $lastSeenTime, '
        'lastActiveTime: $lastActiveTime, '
        'deviceInfo: $deviceInfo, '
        'isTyping: $isTyping, '
        'typingChannelId: $typingChannelId, '
        'createTime: $createTime, '
        'updateTime: $updateTime'
        '}';
  }
}
