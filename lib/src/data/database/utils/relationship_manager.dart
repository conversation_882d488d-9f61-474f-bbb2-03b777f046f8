import 'package:objectbox/objectbox.dart';
import '../entities/entities.dart';
import '../generated/objectbox.g.dart';

/// Utility class for managing ObjectBox relationships
/// Provides helper methods for setting and validating relationships
class RelationshipManager {
  static final RelationshipManager _instance = RelationshipManager._internal();
  factory RelationshipManager() => _instance;
  RelationshipManager._internal();

  Store? _store;

  /// Initialize with ObjectBox store
  void initialize(Store store) {
    _store = store;
  }

  Store get store {
    if (_store == null) {
      throw StateError('RelationshipManager not initialized. Call initialize() first.');
    }
    return _store!;
  }

  /// Find and set session relationship by sessionKey
  bool setSessionRelationship(ToOne<Session> sessionRelation, String sessionKey) {
    if (sessionKey.isEmpty) return false;
    
    final sessionBox = store.box<Session>();
    final session = sessionBox.query(Session_.sessionKey.equals(sessionKey)).build().findFirst();
    
    if (session != null) {
      sessionRelation.target = session;
      return true;
    }
    return false;
  }

  /// Find and set user relationship by userId
  bool setUserRelationship(ToOne<User> userRelation, String userId) {
    if (userId.isEmpty) return false;
    
    final userBox = store.box<User>();
    final user = userBox.query(User_.userId.equals(userId)).build().findFirst();
    
    if (user != null) {
      userRelation.target = user;
      return true;
    }
    return false;
  }

  /// Find and set channel relationship by channelId
  bool setChannelRelationship(ToOne<Channel> channelRelation, String channelId) {
    if (channelId.isEmpty) return false;
    
    final channelBox = store.box<Channel>();
    final channel = channelBox.query(Channel_.channelId.equals(channelId)).build().findFirst();
    
    if (channel != null) {
      channelRelation.target = channel;
      return true;
    }
    return false;
  }

  /// Find and set message relationship by messageId
  bool setMessageRelationship(ToOne<Message> messageRelation, String messageId) {
    if (messageId.isEmpty) return false;
    
    final messageBox = store.box<Message>();
    final message = messageBox.query(Message_.messageId.equals(messageId)).build().findFirst();
    
    if (message != null) {
      messageRelation.target = message;
      return true;
    }
    return false;
  }

  /// Find and set call log relationship by callId
  bool setCallLogRelationship(ToOne<CallLog> callLogRelation, String callId) {
    if (callId.isEmpty) return false;
    
    final callLogBox = store.box<CallLog>();
    final callLog = callLogBox.query(CallLog_.callId.equals(callId)).build().findFirst();
    
    if (callLog != null) {
      callLogRelation.target = callLog;
      return true;
    }
    return false;
  }

  /// Find and set collection relationship by collectionId
  bool setCollectionRelationship(ToOne<Collection> collectionRelation, String collectionId) {
    if (collectionId.isEmpty) return false;
    
    final collectionBox = store.box<Collection>();
    final collection = collectionBox.query(Collection_.collectionId.equals(collectionId)).build().findFirst();
    
    if (collection != null) {
      collectionRelation.target = collection;
      return true;
    }
    return false;
  }

  /// Find and set sticker relationship by stickerId
  bool setStickerRelationship(ToOne<Sticker> stickerRelation, String stickerId) {
    if (stickerId.isEmpty) return false;
    
    final stickerBox = store.box<Sticker>();
    final sticker = stickerBox.query(Sticker_.stickerId.equals(stickerId)).build().findFirst();
    
    if (sticker != null) {
      stickerRelation.target = sticker;
      return true;
    }
    return false;
  }

  /// Validate relationship consistency
  bool validateRelationshipConsistency<T>(
    ToOne<T> relation, 
    String expectedKey, 
    String Function(T) keyExtractor,
  ) {
    final target = relation.target;
    if (target == null) return false;
    return keyExtractor(target) == expectedKey;
  }

  /// Validate session relationship consistency
  bool validateSessionRelationship(ToOne<Session> sessionRelation, String expectedSessionKey) {
    return validateRelationshipConsistency(
      sessionRelation, 
      expectedSessionKey, 
      (session) => session.sessionKey,
    );
  }

  /// Validate user relationship consistency
  bool validateUserRelationship(ToOne<User> userRelation, String expectedUserId) {
    return validateRelationshipConsistency(
      userRelation, 
      expectedUserId, 
      (user) => user.userId,
    );
  }

  /// Validate channel relationship consistency
  bool validateChannelRelationship(ToOne<Channel> channelRelation, String expectedChannelId) {
    return validateRelationshipConsistency(
      channelRelation, 
      expectedChannelId, 
      (channel) => channel.channelId,
    );
  }

  /// Validate message relationship consistency
  bool validateMessageRelationship(ToOne<Message> messageRelation, String expectedMessageId) {
    return validateRelationshipConsistency(
      messageRelation, 
      expectedMessageId, 
      (message) => message.messageId,
    );
  }

  /// Get all entities that reference a specific session
  Map<String, int> getSessionReferenceCount(String sessionKey) {
    final counts = <String, int>{};
    
    // Count users
    final userBox = store.box<User>();
    counts['users'] = userBox.query(User_.sessionKey.equals(sessionKey)).build().count();
    
    // Count session metadata
    final metadataBox = store.box<SessionLocalMetadata>();
    counts['metadata'] = metadataBox.query(SessionLocalMetadata_.sessionKey.equals(sessionKey)).build().count();
    
    // Count channels
    final channelBox = store.box<Channel>();
    counts['channels'] = channelBox.query(Channel_.sessionKey.equals(sessionKey)).build().count();
    
    // Count messages
    final messageBox = store.box<Message>();
    counts['messages'] = messageBox.query(Message_.sessionKey.equals(sessionKey)).build().count();
    
    return counts;
  }

  /// Get all entities that reference a specific user
  Map<String, int> getUserReferenceCount(String userId) {
    final counts = <String, int>{};
    
    // Count profiles
    final profileBox = store.box<Profile>();
    counts['profiles'] = profileBox.query(Profile_.userId.equals(userId)).build().count();
    
    // Count user presence records
    final presenceBox = store.box<UserPresence>();
    counts['presences'] = presenceBox.query(UserPresence_.userId.equals(userId)).build().count();
    
    // Count user status records
    final statusBox = store.box<UserStatus>();
    counts['statuses'] = statusBox.query(UserStatus_.userId.equals(userId)).build().count();
    
    // Count messages authored by user
    final messageBox = store.box<Message>();
    counts['messages'] = messageBox.query(Message_.userId.equals(userId)).build().count();
    
    return counts;
  }

  /// Check if a relationship can be safely deleted
  bool canSafelyDeleteEntity<T>(int entityId, Box<T> box) {
    try {
      final entity = box.get(entityId);
      return entity != null;
    } catch (e) {
      return false;
    }
  }

  /// Cleanup orphaned relationships
  Future<Map<String, int>> cleanupOrphanedRelationships() async {
    final cleaned = <String, int>{};
    
    // This would be implemented based on specific business rules
    // For now, return empty map
    return cleaned;
  }

  /// Batch relationship operations for performance
  void batchSetRelationships<T, R>(
    List<T> entities,
    ToOne<R> Function(T) relationshipGetter,
    String Function(T) keyExtractor,
    bool Function(ToOne<R>, String) relationshipSetter,
  ) {
    for (final entity in entities) {
      final key = keyExtractor(entity);
      final relationship = relationshipGetter(entity);
      relationshipSetter(relationship, key);
    }
  }
}
