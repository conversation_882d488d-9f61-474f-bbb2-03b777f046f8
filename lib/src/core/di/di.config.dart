// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:data_router/src/core/config/config.dart' as _i436;
import 'package:data_router/src/core/database.dart' as _i3;
import 'package:data_router/src/core/di/database_module.dart' as _i137;
import 'package:data_router/src/data/database/generated/objectbox.g.dart'
    as _i535;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  Future<_i174.GetIt> init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) async {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final databaseModule = _$DatabaseModule();
    await gh.lazySingletonAsync<_i535.Store>(
      () => databaseModule.provideObjectBoxStore(),
      preResolve: true,
    );
    gh.lazySingleton<_i436.DataRouterConfig>(() => _i436.DataRouterConfig());
    gh.lazySingleton<_i3.Database>(
        () => databaseModule.provideDatabase(gh<_i535.Store>()));
    return this;
  }
}

class _$DatabaseModule extends _i137.DatabaseModule {}
